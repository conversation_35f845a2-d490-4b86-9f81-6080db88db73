import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { Card } from "primereact/card";
import { Button } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

const MSIActionStatusChart = ({ supplyData = [] }) => {
  const [activeMode, setActiveMode] = useState(true);
  const [chartData, setChartData] = useState([]);

  // Process supply data to extract action status information
  useEffect(() => {
    if (!supplyData || supplyData.length === 0) {
      console.log('MSIActionStatusChart - No supply data, using mock data for testing');
      // Temporary mock data for testing
      const mockData = [
        { status: "Actions Released", count: 33, color: "#3B82F6" },
        { status: "Actions Closed", count: 18, color: "#22C55E" }
      ];
      setChartData(mockData);
      return;
    }

    let actionsReleased = 0;
    let actionsClosed = 0;

    // Process each supplier's actions
    supplyData.forEach(supplier => {
      if (supplier.supplierActions && Array.isArray(supplier.supplierActions)) {
        supplier.supplierActions.forEach(action => {
          // Count all actions as released
          actionsReleased++;
          
          // Count closed actions (type 3)
          if (action.type === 3) {
            actionsClosed++;
          }
        });
      }
    });

    const processedData = [
      {
        status: "Actions Released",
        count: actionsReleased,
        color: "#3B82F6"
      },
      {
        status: "Actions Closed",
        count: actionsClosed,
        color: "#22C55E"
      }
    ];

    setChartData(processedData);
  }, [supplyData]);

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '10px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>{label}</p>
          <p style={{ margin: 0, color: payload[0].color }}>
            Count: {payload[0].value}
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom bar colors
  const getBarColor = (entry, index) => {
    return chartData[index]?.color || "#3B82F6";
  };

  // Calculate completion rate
  const completionRate = chartData.length > 0 ? 
    ((chartData.find(item => item.status === "Actions Closed")?.count || 0) / 
     (chartData.find(item => item.status === "Actions Released")?.count || 1) * 100).toFixed(1) : 0;

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
        <h3 style={{ margin: 0, borderBottom: "2px solid #007bff", paddingBottom: "5px" }}>
          Action Status Overview
        </h3>
        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
          <Button
            icon={activeMode ? "pi pi-table" : "pi pi-chart-bar"}
            className="p-button-text"
            onClick={() => setActiveMode(!activeMode)}
            tooltip={activeMode ? "Switch to Table View" : "Switch to Chart View"}
          />
        </div>
      </div>

      {activeMode ? (
        <ResponsiveContainer width="100%" height={400}>
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="status" 
              fontSize={12}
            />
            <YAxis domain={[0, 'dataMax + 5']} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar
              dataKey="count"
              name="Number of Actions"
              fill="#3B82F6"
              minPointSize={5}
            />
          </BarChart>
        </ResponsiveContainer>
      ) : (
        <div style={{ padding: "20px" }}>
          <DataTable value={chartData} paginator rows={10} sortMode="multiple">
            <Column field="status" header="Status" sortable />
            <Column 
              field="count" 
              header="Count" 
              sortable 
              body={(rowData) => (
                <span style={{ 
                  color: rowData.color, 
                  fontWeight: 'bold' 
                }}>
                  {rowData.count}
                </span>
              )}
            />
          </DataTable>
        </div>
      )}

      {/* Summary */}
      <div style={{ 
        marginTop: "20px", 
        padding: "15px", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "5px" 
      }}>
        <h4 style={{ margin: "0 0 10px 0" }}>Action Status Summary</h4>
        <div style={{ display: "flex", justifyContent: "space-between", flexWrap: "wrap" }}>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Total Released</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#3B82F6" }}>
              {chartData.find(item => item.status === "Actions Released")?.count || 0}
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Total Closed</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#22C55E" }}>
              {chartData.find(item => item.status === "Actions Closed")?.count || 0}
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Completion Rate</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#8B5CF6" }}>
              {completionRate}%
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Pending Actions</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#F59E0B" }}>
              {(chartData.find(item => item.status === "Actions Released")?.count || 0) - 
               (chartData.find(item => item.status === "Actions Closed")?.count || 0)}
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default MSIActionStatusChart;
