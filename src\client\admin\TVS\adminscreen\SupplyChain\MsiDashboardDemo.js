import { Card } from "primereact/card";
import React, { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON><PERSON> } from "primereact/button";
import { Menu } from "primereact/menu";
import FirstBarDemo from "./FirstBarDemo";
import SecondPieDemo from "./SecondPieDemo";
import ThirdLineDemo from "./ThirdLineDemo";
import EnvironmentLineDemo from "./EnvironmentLineDemo";
import SocialLineDemo from "./SocialLineDemo";
import GovernanceLineDemo from "./GovernanceLineDemo";
import MSIActionItemsChart from "./MSIActionItemsChart";
import MSIActionStatusChart from "./MSIActionStatusChart";
import MSISelfAssessmentChart from "./MSISelfAssessmentChart";
import MSIAuditProcessChart from "./MSIAuditProcessChart";
import SubGraph2Demo from "./SubGraph2Demo";
import SubGraph3Demo from "./SubGraph3Demo";
import SubGraph4Demo from "./SubGraph4Demo";
import SubGraph5Demo from "./SubGraph5Demo";
import SupplierTable from "./SupplierTable";
import SubGraph1Demo from "./SubGraph1Demo";
import FiltersSectionDemo from "./FiltersSectionDemo";
// import supplyData from "./supply.json";
import { API } from "../../../../../constants/api_url";
import APIServices from "../../../../../service/APIService";
import AggregatedRadarChart from "./AggregatedRadarChart";

const SuppliersDashboard = () => {
  const [activeMode, setActiveMode] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedbu, setSelectedBU] = useState('All');

  const [supplyData, setSupplyData] = useState([]);
  const [filteredSupplyData, setFilteredSupplyData] = useState([]);
  const [selfAssessmentData, setSelfAssessmentData] = useState([]);
  const [supplierActionsData, setSupplierActionsData] = useState([]);
  const [latestSelfAssessmentScore, setLatestSelfAssessmentScore] = useState(null);
  const [latestMSICalibrationScore, setLatestMSICalibrationScore] = useState(null);

  // Memoize setFilteredSupplyData to prevent infinite loops
  const handleSetFilteredSupplyData = useCallback((data) => {
    setFilteredSupplyData(data);
  }, []);

  useEffect(() => {
    getSupplyData();
    getSelfAssessmentData();
    getSupplierActionsData();
  }, []);

  const getSupplyData = async () => {
    try {
      const response = await APIServices.get(API.supplierResponse);
      if (response.data) {
        console.log(response.data, ' R D')
        setSupplyData(response.data);
      }
    } catch (error) {
      console.log("Error fetching supply data: ", error);
    }
  }

  const getSelfAssessmentData = async () => {
    try {
      const uriString = {
        "include": [
          {
            "relation": "vendor"
          }
        ]
      };

      const response = await APIServices.get(
        API.SupplierAssessmentAss_Up_All + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
      );

      console.log('Self Assessment API Response:', response);
      console.log('Self Assessment Data:', response.data);

      if (response.data && Array.isArray(response.data)) {
        setSelfAssessmentData(response.data);
      }
    } catch (error) {
      console.error('Error fetching self-assessment data:', error);
    }
  }

  const getSupplierActionsData = async () => {
    try {
      const uriString = {
        "include": [
          {
            "relation": "supplierAssignmentSubmission",
            scope: { fields: { type: true, supplierMSIScore: true, submitted_on: true, modified_on: true } }
          },
          {
            "relation": "auditorAssignmentSubmission",
            scope: { fields: { type: true, auditorMSIScore: true, submitted_on: true, modified_on: true, id: true, reportMailStatus: true } }
          },
          {
            "relation": "vendor"
          },
          {
            "relation": "supplierActions",
            scope: { include: ['supplierActionHistories'] }
          }
        ]
      };

      const response = await APIServices.get(
        API.Supplier_assessment_assignment + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
      );

      console.log('Supplier Actions API Response:', response);
      console.log('Supplier Actions Data:', response.data);
      console.log('Supplier Actions Data Length:', response.data?.length);

      if (response.data && Array.isArray(response.data)) {
        setSupplierActionsData(response.data);
        console.log('Set Supplier Actions Data:', response.data);
      } else {
        console.log('No supplier actions data received or data is not an array');
      }
    } catch (error) {
      console.error('Error fetching supplier actions data:', error);
    }
  }

  // Function to calculate latest self-assessment score based on filtered data
  const calculateLatestSelfAssessmentScore = (filteredData, assessmentData) => {
    if (!filteredData.length || !assessmentData.length) {
      return null;
    }

    // Get vendor codes from filtered supply data
    const filteredVendorCodes = new Set(
      filteredData.map(item => item.vendor_code?.toString()).filter(Boolean)
    );

    // Filter self-assessment data for vendors in filtered supply data
    const relevantAssessments = assessmentData.filter(assessment =>
      assessment.vendor &&
      assessment.vendor.code &&
      assessment.supplierMSIScore &&
      filteredVendorCodes.has(assessment.vendor.code.toString())
    );

    if (!relevantAssessments.length) {
      return null;
    }

    // Group assessments by vendor code
    const groupedByVendor = {};
    relevantAssessments.forEach(assessment => {
      const vendorCode = assessment.vendor.code.toString();
      if (!groupedByVendor[vendorCode]) {
        groupedByVendor[vendorCode] = [];
      }
      groupedByVendor[vendorCode].push(assessment);
    });

    // Get latest assessment for each vendor and collect scores
    const latestScores = [];
    Object.keys(groupedByVendor).forEach(vendorCode => {
      const vendorAssessments = groupedByVendor[vendorCode];

      // Sort by created_on date (most recent first) and get the latest
      const latestAssessment = vendorAssessments.sort((a, b) =>
        new Date(b.created_on) - new Date(a.created_on)
      )[0];

      latestScores.push(parseFloat(latestAssessment.supplierMSIScore));
    });

    // Calculate average of latest scores
    if (latestScores.length === 0) {
      return null;
    }

    const averageScore = latestScores.reduce((sum, score) => sum + score, 0) / latestScores.length;

    console.log('Latest scores by vendor:', latestScores);
    console.log('Average of latest scores:', averageScore);

    return averageScore;
  };

  // Function to calculate latest MSI calibration score based on filtered data
  const calculateLatestMSICalibrationScore = (filteredData) => {
    if (!filteredData.length) {
      return null;
    }

    // Group supply data by vendor code
    const groupedByVendor = {};
    filteredData.forEach(supplier => {
      if (supplier.vendor_code && supplier.msi_score) {
        const vendorCode = supplier.vendor_code.toString();
        if (!groupedByVendor[vendorCode]) {
          groupedByVendor[vendorCode] = [];
        }
        groupedByVendor[vendorCode].push(supplier);
      }
    });

    // Get latest MSI score for each vendor (assuming audit_start_date represents the latest)
    const latestScores = [];
    Object.keys(groupedByVendor).forEach(vendorCode => {
      const vendorSuppliers = groupedByVendor[vendorCode];

      // Sort by audit_start_date (most recent first) and get the latest
      // Parse date format "DD.MM.YYYY"
      const latestSupplier = vendorSuppliers.sort((a, b) => {
        if (!a.audit_start_date || !b.audit_start_date) return 0;

        const dateA = a.audit_start_date.split('.').reverse().join('-'); // Convert to YYYY-MM-DD
        const dateB = b.audit_start_date.split('.').reverse().join('-');

        return new Date(dateB) - new Date(dateA);
      })[0];

      latestScores.push(parseFloat(latestSupplier.msi_score));
    });

    // Calculate average of latest scores
    if (latestScores.length === 0) {
      return null;
    }

    const averageScore = latestScores.reduce((sum, score) => sum + score, 0) / latestScores.length;

    console.log('Latest MSI calibration scores by vendor:', latestScores);
    console.log('Average of latest MSI calibration scores:', averageScore);

    return averageScore;
  };

  // Function to get MSI grade based on calibration score
  const getMSIGrade = (score) => {
    if (score >= 86) return "Platinum";
    if (score >= 71) return "Gold";
    if (score >= 56) return "Silver";
    return "Not Met";
  };

  const menuRef = useRef(null);
  const panelItems = [
    {
      items: [
        {
          label: "Export as Excel",
          icon: "pi pi-file-excel",
          command: () => {
            // downloadExcelWithImage(chartRef);
          },
        },
        {
          label: "Export as PDF",
          icon: "pi pi-file-pdf",
          command: () => {
            // downloadPdfWithImage(chartRef);
          },
        },
        {
          label: "Export as JPG",
          icon: "pi pi-image",
          command: () => {
            // downloadChartAsJpg(chartRef);
          },
        },
        activeMode && {
          label: "Print",
          icon: "pi pi-print",
          command: () => {
            // printChart(chartRef);
          },
        },
      ],
    },
  ];

  useEffect(() => {
    if (supplyData.length > 0) {
      // Rank the data initially (without filtering)
      let rankedData = [...supplyData].sort(
        (a, b) => b.msi_score - a.msi_score
      );

      rankedData = rankedData.map((item, index) => ({
        ...item,
        rank: index + 1, // Rank starts from 1
      }));

      setFilteredSupplyData(rankedData); // Set the initial filtered data to the ranked data
    }
  }, [supplyData]);

  // Removed this useEffect as it was causing unnecessary re-renders
  // The filtering is now handled by FiltersSectionDemo component

  // Update latest scores when filtered data or self-assessment data changes
  useEffect(() => {
    const latestSelfAssessment = calculateLatestSelfAssessmentScore(filteredSupplyData, selfAssessmentData);
    const latestMSICalibration = calculateLatestMSICalibrationScore(filteredSupplyData);

    setLatestSelfAssessmentScore(latestSelfAssessment);
    setLatestMSICalibrationScore(latestMSICalibration);

    console.log('Latest Self Assessment Score for filtered data:', latestSelfAssessment);
    console.log('Latest MSI Calibration Score for filtered data:', latestMSICalibration);
  }, [filteredSupplyData, selfAssessmentData]);

  return (
    <>
      <FirstBarDemo supplyData={supplyData} />
      <SecondPieDemo supplyData={supplyData} />
      <ThirdLineDemo supplyData={supplyData} />
      <div className="row">
        <div className="col-6">
          <EnvironmentLineDemo supplyData={supplyData} />
        </div>
        <div className="col-6">
          <SocialLineDemo supplyData={supplyData} />
        </div>
        <div className="col-6">
          <GovernanceLineDemo supplyData={supplyData} />
        </div>
      </div>

      {/* MSI Actions Analytics */}
      <div className="row mt-3">
        <div className="col-12">
          <h2 style={{ borderBottom: "2px solid #007bff", paddingBottom: "10px", marginBottom: "20px" }}>
            MSI Actions Analytics
          </h2>
        </div>
        <div className="col-md-6">
          <MSIActionItemsChart supplyData={supplierActionsData} />
        </div>
        <div className="col-md-6">
          <MSIActionStatusChart supplyData={supplierActionsData} />
        </div>
      </div>

      {/* MSI Audit Analytics */}
      <div className="row mt-3">
        <div className="col-12">
          <h2 style={{ borderBottom: "2px solid #28a745", paddingBottom: "10px", marginBottom: "20px" }}>
            MSI Audit Analytics
          </h2>
        </div>
        <div className="col-md-6">
          <MSISelfAssessmentChart supplyData={supplierActionsData} />
        </div>
        <div className="col-md-6">
          <MSIAuditProcessChart supplyData={supplierActionsData} />
        </div>
      </div>

      <Card>
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <h3>MSI Performance Analytics</h3>
          <div
            style={{
              margin: "18px 10px 18px 10px",
              display: "flex",
            }}
          >
            <div
              className="buttons"
              style={{
                background: "#F0F2F4",
                borderRadius: "3px",
                width: "4.5rem",
                marginLeft: "10px",
                height: "30px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Button
                style={{
                  background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                  padding: "6px",
                  color: "black",
                  border: "0px",
                  marginRight: "4px",
                }}
                onClick={() => {
                  setActiveMode(false);
                }}
              >
                <i className="pi pi-table fs-19" />
              </Button>
              <Button
                style={{
                  background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                  padding: "6px",
                  color: "black",
                  border: "0px",
                }}
                onClick={() => {
                  setActiveMode(true);
                }}
              >
                <i className="pi pi-chart-bar fs-19" />
              </Button>
            </div>
            <div ref={menuRef}>
              <Button
                style={{
                  color: "black",
                  height: "30px",
                  marginLeft: "3px",
                  background: "#F0F2F4",
                  border: "0px",
                  padding: "6px",
                  position: "relative",
                }}
                onClick={() => {
                  setDropdownOpen(!dropdownOpen);
                }}
              >
                <i className="pi pi-angle-down fs-19" />
              </Button>
              {dropdownOpen && (
                <Menu
                  model={panelItems}
                  style={{
                    position: "absolute",
                    right: 45,
                    zIndex: "1",
                    padding: 0,
                  }}
                ></Menu>
              )}
            </div>
          </div>
        </div>
        <FiltersSectionDemo
          supplyData={supplyData} setSelectedBu={(e) => { setSelectedBU(e) }}
          setFilteredSupplyData={handleSetFilteredSupplyData}
        />
        <Card style={{ height: "120px", backgroundColor: "#FAFAFA" }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-evenly",
            }}
          >
            <h3 style={{ marginLeft: "-180px" }}>Summary</h3>
            <div style={{ display: "flex", flexDirection: "column" }}>
              <p style={{ fontSize: "18px", fontWeight: 600 }}>
                Self Assessment Score
              </p>
              <p
                style={{
                  fontSize: "20px",
                  fontWeight: 700,
                  textAlign: "center",
                  color: "#F59E0B"
                }}
              >
                {latestSelfAssessmentScore !== null
                  ? latestSelfAssessmentScore.toFixed(2)
                  : "N/A"}
              </p>
            </div>
            <div style={{ display: "flex", flexDirection: "column" }}>
              <p style={{ fontSize: "18px", fontWeight: 600 }}>
                MSI Calibration Score
              </p>
              <p
                style={{
                  fontSize: "20px",
                  fontWeight: 700,
                  textAlign: "center",
                  color: "#6C480B"
                }}
              >
                {latestMSICalibrationScore !== null
                  ? latestMSICalibrationScore.toFixed(2)
                  : "N/A"}
              </p>
            </div>
            <div style={{ display: "flex", flexDirection: "column" }}>
              <p style={{ fontSize: "18px", fontWeight: 600 }}>MSI Grade</p>
              <p
                style={{
                  fontSize: "20px",
                  fontWeight: 700,
                  textAlign: "center",
                  color: latestMSICalibrationScore !== null
                    ? getMSIGrade(latestMSICalibrationScore) === "Platinum" ? "#E5E7EB"
                      : getMSIGrade(latestMSICalibrationScore) === "Gold" ? "#F59E0B"
                        : getMSIGrade(latestMSICalibrationScore) === "Silver" ? "#9CA3AF"
                          : "#EF4444"
                    : "#6B7280"
                }}
              >
                {latestMSICalibrationScore !== null
                  ? getMSIGrade(latestMSICalibrationScore)
                  : "N/A"}
              </p>
            </div>
          </div>
        </Card>
        <div className="row mt-3">
          <div className="p-2 border card col-md-6">
            <SubGraph1Demo supplyData={filteredSupplyData} />
          </div>
          <div className="p-2 border card col-md-6" style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
            <SubGraph2Demo selectedSite={selectedbu} supplyData={filteredSupplyData} />
          </div>
        </div>
        <div className="row">
          <div className="p-2 border card col-md-12">
            <AggregatedRadarChart filteredSupplyData={filteredSupplyData} />
          </div>
        </div>
        {/*
        <div style={{ display: "flex", marginTop: "30px", maxWidth: "100%" }}>
          <SubGraph1Demo supplyData={filteredSupplyData} />

          <SubGraph2Demo selectedSite={selectedbu} supplyData={filteredSupplyData} />
        </div>
        <div style={{
          display: "flex",
          width: "100%",
          margin: "30px auto",
          justifyContent: "center",
          alignItems: "center"
        }}>
          <AggregatedRadarChart filteredSupplyData={filteredSupplyData} />
        </div> */}

      </Card>
      {/* <Card>
        <div style={{ display: "flex" }}>
          <SubGraph3Demo supplyData={filteredSupplyData} />
          <SubGraph4Demo supplyData={filteredSupplyData} />
          <SubGraph5Demo supplyData={filteredSupplyData} />
        </div>
      </Card> */}
      <div className="row mt-3">
        <div className="p-2 border card col-md-4">
          <SubGraph3Demo supplyData={filteredSupplyData} />
        </div>
        <div className="p-2 border card col-md-4">
          <SubGraph4Demo supplyData={filteredSupplyData} />
        </div>
        <div className="p-2 border card col-md-4">
          <SubGraph5Demo supplyData={filteredSupplyData} />
        </div>
      </div>


      <div className="row">
        <div className="p-2 border card col-md-12" style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
          <SupplierTable supplyData={filteredSupplyData} />
        </div>
      </div>

    </>
  );
};

export default SuppliersDashboard;
