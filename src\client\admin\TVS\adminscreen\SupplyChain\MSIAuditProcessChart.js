import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { Card } from "primereact/card";
import { Button } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

const MSIAuditProcessChart = ({ supplyData = [] }) => {
  const [activeMode, setActiveMode] = useState(true);
  const [chartData, setChartData] = useState([]);

  // Process supply data to extract audit process information
  useEffect(() => {
    if (!supplyData || supplyData.length === 0) {
      setChartData([]);
      return;
    }

    let auditScheduled = 0;
    let auditConducted = 0;
    let reportsReleased = 0;
    let reportsAcknowledged = 0;
    let actionPlanSubmitted = 0;

    // Process each supplier
    supplyData.forEach(supplier => {
      // Count as scheduled if there's an audit assignment
      if (supplier.auditStartDate || supplier.auditEndDate) {
        auditScheduled++;
        
        // Count as conducted if there's an auditor submission
        if (supplier.auditorAssignmentSubmission) {
          auditConducted++;
          
          // Count as report released if there's an auditor score
          if (supplier.auditorAssignmentSubmission.auditorMSIScore) {
            reportsReleased++;
            
            // Count as acknowledged if report mail status is set
            if (supplier.auditorAssignmentSubmission.reportMailStatus) {
              reportsAcknowledged++;
            }
          }
        }
        
        // Count action plan submitted if there are supplier actions
        if (supplier.supplierActions && supplier.supplierActions.length > 0) {
          actionPlanSubmitted++;
        }
      }
    });

    const processedData = [
      {
        stage: "Audit Scheduled",
        count: auditScheduled,
        color: "#6366F1"
      },
      {
        stage: "Audits Conducted",
        count: auditConducted,
        color: "#3B82F6"
      },
      {
        stage: "Audit Reports Released",
        count: reportsReleased,
        color: "#22C55E"
      },
      {
        stage: "Reports Acknowledged by Supplier",
        count: reportsAcknowledged,
        color: "#F59E0B"
      },
      {
        stage: "Action Plan Submitted by Supplier",
        count: actionPlanSubmitted,
        color: "#8B5CF6"
      }
    ];

    setChartData(processedData);
  }, [supplyData]);

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '10px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>{label}</p>
          <p style={{ margin: 0, color: payload[0].color }}>
            Count: {payload[0].value}
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom bar component to use individual colors
  const CustomBar = (props) => {
    const { fill, ...rest } = props;
    const color = props.payload?.color || fill;
    return <Bar {...rest} fill={color} />;
  };

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
        <h3 style={{ margin: 0, borderBottom: "2px solid #28a745", paddingBottom: "5px" }}>
          Audit Process Analytics
        </h3>
        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
          <Button
            icon={activeMode ? "pi pi-table" : "pi pi-chart-bar"}
            className="p-button-text"
            onClick={() => setActiveMode(!activeMode)}
            tooltip={activeMode ? "Switch to Table View" : "Switch to Chart View"}
          />
        </div>
      </div>

      {activeMode ? (
        <ResponsiveContainer width="100%" height={400}>
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 80 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="stage" 
              angle={-45}
              textAnchor="end"
              height={120}
              fontSize={11}
            />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar 
              dataKey="count" 
              name="Number of Suppliers"
              shape={<CustomBar />}
            />
          </BarChart>
        </ResponsiveContainer>
      ) : (
        <div style={{ padding: "20px" }}>
          <DataTable value={chartData} paginator rows={10} sortMode="multiple">
            <Column field="stage" header="Audit Stage" sortable />
            <Column 
              field="count" 
              header="Count" 
              sortable 
              body={(rowData) => (
                <span style={{ 
                  color: rowData.color, 
                  fontWeight: 'bold' 
                }}>
                  {rowData.count}
                </span>
              )}
            />
          </DataTable>
        </div>
      )}

      {/* Summary */}
      <div style={{ 
        marginTop: "20px", 
        padding: "15px", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "5px" 
      }}>
        <h4 style={{ margin: "0 0 10px 0" }}>Audit Process Summary</h4>
        <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(120px, 1fr))", gap: "15px" }}>
          {chartData.map((item, index) => (
            <div key={index} style={{ textAlign: "center" }}>
              <p style={{ margin: "5px 0", fontSize: "12px", color: "#666" }}>{item.stage}</p>
              <p style={{ margin: "0", fontSize: "18px", fontWeight: "bold", color: item.color }}>
                {item.count}
              </p>
            </div>
          ))}
        </div>
        
        {/* Process completion rate */}
        <div style={{ marginTop: "15px", textAlign: "center" }}>
          <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Overall Process Completion Rate</p>
          <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#8B5CF6" }}>
            {chartData.length > 0 && chartData[0].count > 0 ? 
              ((chartData[chartData.length - 1]?.count || 0) / chartData[0].count * 100).toFixed(1) : 0}%
          </p>
        </div>
      </div>
    </Card>
  );
};

export default MSIAuditProcessChart;
