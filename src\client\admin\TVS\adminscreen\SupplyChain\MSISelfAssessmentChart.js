import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { Card } from "primereact/card";
import { Button } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

const MSISelfAssessmentChart = ({ supplyData = [] }) => {
  const [activeMode, setActiveMode] = useState(true);
  const [chartData, setChartData] = useState([]);

  // Process supply data to extract self-assessment information
  useEffect(() => {
    if (!supplyData || supplyData.length === 0) {
      console.log('MSISelfAssessmentChart - No supply data, using mock data for testing');
      // Temporary mock data for testing
      const mockData = [
        { category: "Self Assessment Scheduled", count: 25, color: "#3B82F6" },
        { category: "Self Assessment Completed", count: 18, color: "#22C55E" }
      ];
      setChartData(mockData);
      return;
    }

    let selfAssessmentScheduled = 0;
    let selfAssessmentCompleted = 0;

    // Process each supplier
    supplyData.forEach(supplier => {
      // Count as scheduled if there's an assessment assignment
      if (supplier.supplierAssignmentSubmission || supplier.assessmentStartDate || supplier.assessmentEndDate) {
        selfAssessmentScheduled++;
        
        // Count as completed if there's a submission with a score
        if (supplier.supplierAssignmentSubmission && 
            supplier.supplierAssignmentSubmission.supplierMSIScore) {
          selfAssessmentCompleted++;
        }
      }
    });

    const processedData = [
      {
        category: "Self Assessment Scheduled",
        count: selfAssessmentScheduled,
        color: "#3B82F6"
      },
      {
        category: "Self Assessment Completed",
        count: selfAssessmentCompleted,
        color: "#22C55E"
      }
    ];

    setChartData(processedData);
  }, [supplyData]);

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '10px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>{label}</p>
          <p style={{ margin: 0, color: payload[0].color }}>
            Count: {payload[0].value}
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom bar component to use individual colors
  const CustomBar = (props) => {
    const { fill, ...rest } = props;
    const color = props.payload?.color || fill;
    return <Bar {...rest} fill={color} />;
  };

  // Calculate completion rate
  const completionRate = chartData.length > 0 ? 
    ((chartData.find(item => item.category === "Self Assessment Completed")?.count || 0) / 
     (chartData.find(item => item.category === "Self Assessment Scheduled")?.count || 1) * 100).toFixed(1) : 0;

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
        <h3 style={{ margin: 0, borderBottom: "2px solid #28a745", paddingBottom: "5px" }}>
          Self Assessment Analytics
        </h3>
        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
          <Button
            icon={activeMode ? "pi pi-table" : "pi pi-chart-bar"}
            className="p-button-text"
            onClick={() => setActiveMode(!activeMode)}
            tooltip={activeMode ? "Switch to Table View" : "Switch to Chart View"}
          />
        </div>
      </div>

      {activeMode ? (
        <ResponsiveContainer width="100%" height={400}>
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="category" 
              angle={-45}
              textAnchor="end"
              height={100}
              fontSize={12}
            />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar 
              dataKey="count" 
              name="Number of Suppliers"
              shape={<CustomBar />}
            />
          </BarChart>
        </ResponsiveContainer>
      ) : (
        <div style={{ padding: "20px" }}>
          <DataTable value={chartData} paginator rows={10} sortMode="multiple">
            <Column field="category" header="Category" sortable />
            <Column 
              field="count" 
              header="Count" 
              sortable 
              body={(rowData) => (
                <span style={{ 
                  color: rowData.color, 
                  fontWeight: 'bold' 
                }}>
                  {rowData.count}
                </span>
              )}
            />
          </DataTable>
        </div>
      )}

      {/* Summary */}
      <div style={{ 
        marginTop: "20px", 
        padding: "15px", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "5px" 
      }}>
        <h4 style={{ margin: "0 0 10px 0" }}>Self Assessment Summary</h4>
        <div style={{ display: "flex", justifyContent: "space-between", flexWrap: "wrap" }}>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Scheduled</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#3B82F6" }}>
              {chartData.find(item => item.category === "Self Assessment Scheduled")?.count || 0}
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Completed</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#22C55E" }}>
              {chartData.find(item => item.category === "Self Assessment Completed")?.count || 0}
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Completion Rate</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#8B5CF6" }}>
              {completionRate}%
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Pending</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#F59E0B" }}>
              {(chartData.find(item => item.category === "Self Assessment Scheduled")?.count || 0) - 
               (chartData.find(item => item.category === "Self Assessment Completed")?.count || 0)}
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default MSISelfAssessmentChart;
