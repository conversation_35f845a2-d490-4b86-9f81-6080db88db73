import React, { useState, useEffect } from "react";
import SideBarGraph from "./SidebarGraph";
import { Card } from "primereact/card";
import EnvironmentChart from "./EnvironmentChart";
import SocialChart from "./SocialChart";
import Governance<PERSON>hart from "./GovernanceChart";
import <PERSON><PERSON><PERSON> from "./GeneralChart";
import MS<PERSON><PERSON><PERSON><PERSON> from "./MSIGrade<PERSON>hart";
import APSMSIG<PERSON>Chart from "./APSMSIGradeChart";
import AOfficeMS<PERSON><PERSON><PERSON><PERSON> from "./AOfficeMSIGradeChart";
import FilterComponent from "./FilterComponent";
import ESGGChart from "./ESGGChart";
import ESGGOverviewChart from "./ESGGOverviewChart";
import ESGGScores from "./ESGGScores"
import MSIScoresOverTime from "./MSIScoresOverTime";
import { Tabs, Tab } from "@mui/material";
import SideBarTab from "./TabGraphs/SideBarTab";
import MSIScoresOverTimeTab from "./TabGraphs/MSIScoresOverTimeTab";
import APSESGOverviewTab from "./TabGraphs/APSESGOverviewTab";
import AOESGOverviewTab from "./TabGraphs/AOESGOverviewTab";
import ESGScoresTab from "./TabGraphs/ESGScoresTab";
import EnvironmentTab from "./TabGraphs/EnvironmentTab";
import SocialTab from "./TabGraphs/SocialTab";
import GovernanceTab from "./TabGraphs/GovernanceTab";
import { API } from "../../../../../constants/api_url";
import APIServices from "../../../../../service/APIService";
import moment from "moment";

const DealerDashboard = () => {
  const [data, setData] = useState([]);
  const [apsData, setApsData] = useState([]);
  const [aoData, setAoData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [filteredApsData, setFilteredApsData] = useState([]);
  const [filteredAoData, setFilteredAoData] = useState([]);
  const [tabIndex, setTabIndex] = useState(0); // Track the active tab
  useEffect(() => {
    getDealerData();
  }, []);


  const getDealerData = async () => {
    try {
      const response = await APIServices.get(API.dealerResponse);
      console.log("API Response:", response.data);

      if (response.data) {
        // Filter for regular dealers (dealerType 1 or 2)
        const dealerFiltered = response.data.filter(item => item.dealerType === 1 || item.dealerType === 2);
        console.log("Dealer Data (Type 1 & 2):", dealerFiltered);

        // Filter for APS (dealerType 3)
        const apsFiltered = response.data.filter(item => item.dealerType === 3);
        console.log("APS Data (Type 3):", apsFiltered);

        // Filter for Area Office (dealerType 4)
        const aoFiltered = response.data.filter(item => item.dealerType === 4);
        console.log("Area Office Data (Type 4):", aoFiltered);

        // Process dealer data
        const grouped = {};

        // Group by branch_code and category
        dealerFiltered.forEach(item => {
          const branch = item.branch_code;
          const category = item.category;

          if (!grouped[branch]) grouped[branch] = {};
          if (!grouped[branch][category]) {
            grouped[branch][category] = item;
          } else {
            const existingDate = moment(grouped[branch][category].date_of_calibration, 'YYYY-MM-DD');
            const currentDate = moment(item.date_of_calibration, 'YYYY-MM-DD');
            if (currentDate.isAfter(existingDate)) {
              grouped[branch][category] = item;
            }
          }
        });

        // Collect branches that have both Sales and Service categories
        const dealerResult = [];

        Object.values(grouped).forEach(categoryMap => {
          if (categoryMap['Sales'] && categoryMap['Service']) {
            dealerResult.push(categoryMap['Sales']);
            dealerResult.push(categoryMap['Service']);
          }
        });

        // Set dealer data
        setFilteredData(dealerResult);
        setData(dealerResult);

        // Set APS data
        setApsData(apsFiltered);
        setFilteredApsData(apsFiltered);

        // Set Area Office data
        setAoData(aoFiltered);
        setFilteredAoData(aoFiltered);
      }
    } catch (error) {
      console.log("Error fetching data: ", error);
      setFilteredData([]);  // Ensures it never remains undefined
      setData([]);
      setApsData([]);
      setFilteredApsData([]);
      setAoData([]);
      setFilteredAoData([]);
    }
  }



  const handleTabChange = (_, newValue) => {
    setTabIndex(newValue);
  };



  const renderTab2Content = () => (
    <div>
      <div className="row">
        <SideBarTab tabIndex={tabIndex} />
      </div>
      <div className="row">
        <MSIScoresOverTimeTab tabIndex={tabIndex} />
      </div>
      <br />
      <div className="p-2 border card col-md-12">
        <FilterComponent
          type={'aps'}
          data={apsData}
          setFilteredData={setFilteredApsData}
          tabIndex={tabIndex}
        />
      </div>
      <div className="card">
        <APSESGOverviewTab data={filteredApsData} />
      </div>
      <div className="row">
        <div className="p-2 border card col-md-6">
          <ESGScoresTab data={filteredApsData} tabIndex={tabIndex} />
        </div>
        <div className="p-2 border card col-md-6">
          <APSMSIGradeChart data={filteredApsData} />
        </div>
      </div>
      <div className="row">
        <div className="p-2 border card col-md-4">
          <EnvironmentTab data={filteredApsData} tabIndex={tabIndex} />
        </div>
        <div className="p-2 border card col-md-4">
          <SocialTab data={filteredApsData} tabIndex={tabIndex} />
        </div>
        <div className="p-2 border card col-md-4">
          <GovernanceTab data={filteredApsData} tabIndex={tabIndex} />
        </div>
      </div>
    </div>
  );

  const renderTab1Content = () => (
    <div>
      <div className="row">
        <SideBarGraph data={filteredData} />
      </div>
      <div className="row">
        <MSIScoresOverTime />
      </div>
      <br />
      <div className="p-2 border card col-md-12">
        <FilterComponent
          type={'dealer'}
          data={data}
          setFilteredData={setFilteredData}
          tabIndex={tabIndex}
        />
      </div>
      <div className="row">
        <div className="p-2 border card col-md-6">
          <ESGGOverviewChart data={filteredData} />
        </div>

        <div className="p-2 border card col-md-6">
          <MSIGradeChart data={filteredData} />
        </div>
      </div>
      <div className="row">
        <div className="p-2 border card col-md-4">
          <ESGGScores data={filteredData} />
        </div>

        <div className="p-2 border card col-md-8">
          <ESGGChart data={filteredData} />
        </div>
      </div>
      <div className="row">
        <div className="p-2 border card col-md-12">
          <EnvironmentChart data={filteredData} />
        </div>

        <div className="p-2 border card col-md-12">
          <SocialChart data={filteredData} />
        </div>
      </div>
      <div className="row">
        <div className="p-2 border card col-md-12">
          <GovernanceChart data={filteredData} />
        </div>

        <div className="p-2 border card col-md-12">
          <GeneralChart data={filteredData} />
        </div>
      </div>
    </div>
  );

  const renderTab3Content = () => (
    <div>
      <div className="row">
        <SideBarTab tabIndex={tabIndex} />
      </div>
      <div className="row">
        <MSIScoresOverTimeTab tabIndex={tabIndex} />
      </div>
      <br />
      <div className="p-2 border card col-md-12">
        <FilterComponent
          type={'ao'}
          data={aoData}
          setFilteredData={setFilteredAoData}
          tabIndex={tabIndex}
        />
      </div>
      <div className="card">
        <AOESGOverviewTab data={filteredAoData} />
      </div>
      <div className="row">
        <div className="p-2 border card col-md-6">
          <ESGScoresTab data={filteredAoData} tabIndex={tabIndex} />
        </div>
        <div className="p-2 border card col-md-6">
          <AOfficeMSIGradeChart data={filteredAoData} />
        </div>
      </div>
      <div className="row">
        <div className="p-2 border card col-md-4">
          <EnvironmentTab data={filteredAoData} tabIndex={tabIndex} />
        </div>
        <div className="p-2 border card col-md-4">
          <SocialTab data={filteredAoData} tabIndex={tabIndex} />
        </div>
        <div className="p-2 border card col-md-4">
          <GovernanceTab data={filteredAoData} tabIndex={tabIndex} />
        </div>
      </div>
    </div>
  );

  return (
    <div>
      <Tabs
        value={tabIndex}
        onChange={handleTabChange}
        aria-label="AMD, ADO, AO tabs"
      >
        <Tab label="Dealers" />
        <Tab label="APS" />
        <Tab label="Area Office" />
      </Tabs>

      {/* Render content based on selected tab */}
      {tabIndex === 0 && <div>{renderTab1Content()}</div>}
      {tabIndex === 1 && <div>{renderTab2Content()}</div>}
      {tabIndex === 2 && <div>{renderTab3Content()}</div>}
    </div>
  );
};

export default DealerDashboard;
